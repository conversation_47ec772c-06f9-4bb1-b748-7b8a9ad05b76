import 'package:flutter/material.dart';
import 'HeaderComponent.dart';
import 'GameListComponent.dart';
import 'FooterComponent.dart';

class GameLobbyLayout extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1272,
      decoration: BoxDecoration(
        color: Color(0xFF004447),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: Color(0xCC000000), width: 2.5),
      ),
      child: Column(
        children: [
          HeaderComponent(),
          Expanded(
            child: GameListComponent(
              games: [
                {
                  'type': 'Texas Hold\'em',
                  'name': 'Bluey World Series Poker Tournament',
                  'blinds': '\$20',
                  'buyIn': '\$2,500',
                  'players': '8',
                  'wait': '0:00',
                  'hours': '4.3',
                },
                // Add more games as needed
              ],
            ),
          ),
          FooterComponent(),
        ],
      ),
    );
  }
}

