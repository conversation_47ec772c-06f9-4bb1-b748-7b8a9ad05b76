import 'package:flutter/material.dart';
import 'header_styles.dart';

class HeaderComponent extends StatelessWidget {
  final double width;
  final double height;

  const HeaderComponent({
    Key? key,
    this.width = 1272,
    this.height = 60,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Color(0xFF004447),
        borderRadius: BorderRadius.circular(24),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left section
          Container(
            child: Row(
              children: [
                Image.network(
                  'https://dashboard.codeparrot.ai/api/image/aFK13_SsIdXXVhRh/frame.png',
                  width: 40,
                  height: 40,
                  fit: BoxFit.cover,
                ),
              ],
            ),
          ),
          
          // Center section
          Expanded(
            child: Container(
              alignment: Alignment.center,
              child: Text(
                'GAME LOBBY',
                style: headerTitleStyle,
              ),
            ),
          ),
          
          // Right section
          Container(
            width: 40,
            height: 40,
          ),
        ],
      ),
    );
  }
}

