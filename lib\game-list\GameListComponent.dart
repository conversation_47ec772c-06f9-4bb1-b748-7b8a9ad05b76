import 'package:flutter/material.dart';

class GameListComponent extends StatelessWidget {
  final List<Map<String, String>> games;

  const GameListComponent({Key? key, required this.games}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Headers
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Row(
            children: [
              _buildHeader('Type', width: 95, left: true),
              _buildHeader('Name', width: 200),
              _buildHeader('Blinds', width: 95),
              _buildHeader('Buy-In', width: 95),
              _buildHeader('Players', width: 95),
              _buildHeader('WAIT', width: 95),
              _buildHeader('Hours', width: 95),
              _buildHeader('Action', width: 202),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // Game List
        Expanded(
          child: ListView.builder(
            itemCount: games.length,
            itemBuilder: (context, index) {
              bool isAlternate = index % 2 == 0;
              return _buildGameRow(games[index], isAlternate);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(String text, {required double width, bool left = false}) {
    return Container(
      width: width,
      child: Text(
        text,
        style: const TextStyle(
          fontFamily: 'Inter',
          fontSize: 12,
          fontWeight: FontWeight.w800,
          color: Colors.white,
          letterSpacing: 0.5,
        ),
        textAlign: left ? TextAlign.left : TextAlign.center,
      ),
    );
  }

  Widget _buildGameRow(Map<String, String> game, bool isAlternate) {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: isAlternate ? const Color(0xFF004F52) : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0x29FFFFFF),
          width: 2,
        ),
      ),
      child: Row(
        children: [
          _buildGameText(game['type']!, width: 95, left: true),
          _buildGameText(game['name']!, width: 200),
          _buildGameText(game['blinds']!, width: 95),
          _buildGameText(game['buyIn']!, width: 95),
          _buildGameText(game['players']!, width: 95),
          _buildGameText(game['wait']!, width: 95),
          _buildGameText(game['hours']!, width: 95),
          Container(
            width: 202,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _buildJoinButton(),
                const SizedBox(width: 8),
                _buildInfoButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameText(String text, {required double width, bool left = false}) {
    return Container(
      width: width,
      child: Text(
        text,
        style: const TextStyle(
          fontFamily: 'Inter',
          fontSize: 14,
          fontWeight: FontWeight.w800,
          color: Colors.white,
        ),
        textAlign: left ? TextAlign.left : TextAlign.center,
      ),
    );
  }

  Widget _buildJoinButton() {
    return Container(
      height: 48,
      child: ElevatedButton(
        onPressed: () {},
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF33A076),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: const BorderSide(
              color: Color(0xCC000000),
              width: 2.5,
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16),
        ),
        child: const Text(
          'JOIN GAME',
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: 16,
            fontWeight: FontWeight.w900,
            fontStyle: FontStyle.italic,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildInfoButton() {
    return Container(
      width: 52,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        icon: Image.network('https://dashboard.codeparrot.ai/api/image/aFK13_SsIdXXVhRh/action-b.png'),
        onPressed: () {},
      ),
    );
  }
}

